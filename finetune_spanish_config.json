{"project_name": "mms_spanish_finetuning_local", "push_to_hub": false, "hub_model_id": "my-spanish-tts-model", "report_to": [], "overwrite_output_dir": true, "output_dir": "./tmp/vits_finetuned_spanish", "dataset_name": "../spanish_dataset.py", "dataset_config_name": "default", "audio_column_name": "audio", "text_column_name": "text", "train_split_name": "train", "eval_split_name": "train", "speaker_id_column_name": null, "override_speaker_embeddings": false, "filter_on_speaker_id": null, "full_generation_sample_text": "<PERSON><PERSON>, ¿cómo estás hoy? Espero que tengas un buen día.", "max_duration_in_seconds": 20, "min_duration_in_seconds": 1.0, "max_tokens_length": 500, "model_name_or_path": "ylacombe/mms-tts-spa-train", "preprocessing_num_workers": 4, "do_train": true, "num_train_epochs": 50, "gradient_accumulation_steps": 1, "gradient_checkpointing": false, "per_device_train_batch_size": 8, "learning_rate": 2e-05, "adam_beta1": 0.8, "adam_beta2": 0.99, "warmup_ratio": 0.01, "group_by_length": false, "do_eval": true, "eval_steps": 100, "per_device_eval_batch_size": 8, "max_eval_samples": 50, "do_step_schedule_per_epoch": true, "weight_disc": 3, "weight_fmaps": 1, "weight_gen": 1, "weight_kl": 1.5, "weight_duration": 1, "weight_mel": 35, "fp16": true, "seed": 42, "save_steps": 500, "logging_steps": 50, "save_total_limit": 3}