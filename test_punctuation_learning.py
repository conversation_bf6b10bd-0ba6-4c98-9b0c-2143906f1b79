#!/usr/bin/env python3
"""
Test específico para verificar si el modelo aprendió puntuación y pausas
"""

import torch
from transformers import pipeline
import soundfile as sf
import numpy as np
from datetime import datetime
import os

def test_punctuation_learning():
    """Test específico de aprendizaje de puntuación"""
    
    model_path = "./finetune-hf-vits/tmp/vits_finetuned_spanish"
    
    print("🔍 VERIFICANDO APRENDIZAJE DE PUNTUACIÓN")
    print("=" * 50)
    
    try:
        device = 0 if torch.cuda.is_available() else -1
        tts = pipeline("text-to-speech", model=model_path, device=device)
        
        # Tests específicos de puntuación (basados en tu dataset)
        punctuation_tests = [
            {
                "without": "Tengo diecisiete años me llamo <PERSON> estoy en el primer semestre",
                "with": "Tengo diecisiete años, me llamo <PERSON>, estoy en el primer semestre.",
                "type": "Comas y punto final"
            },
            {
                "without": "No se muy bien en que consiste el realismo visceral",
                "with": "No sé muy bien en qué consiste el realismo visceral.",
                "type": "Acentos y punto final"
            },
            {
                "without": "Por supuesto he aceptado",
                "with": "Por supuesto, he aceptado.",
                "type": "Coma y punto"
            },
            {
                "without": "Que paso ayer en la reunion",
                "with": "¿Qué pasó ayer en la reunión?",
                "type": "Interrogación"
            },
            {
                "without": "Excelente trabajo muy bien hecho",
                "with": "¡Excelente trabajo! Muy bien hecho.",
                "type": "Exclamación y punto"
            }
        ]
        
        output_dir = "punctuation_test"
        os.makedirs(output_dir, exist_ok=True)
        
        print(f"🎵 Generando {len(punctuation_tests)} pares de comparación...")
        
        for i, test in enumerate(punctuation_tests):
            print(f"\n📝 Test {i+1}: {test['type']}")
            print(f"   Sin puntuación: '{test['without']}'")
            print(f"   Con puntuación: '{test['with']}'")
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Generar versión sin puntuación
            try:
                result_without = tts(test['without'])
                waveform_without = result_without["audio"]
                sampling_rate = result_without["sampling_rate"]
                
                if isinstance(waveform_without, torch.Tensor):
                    waveform_without = waveform_without.cpu().numpy()
                
                if len(waveform_without.shape) > 1:
                    waveform_without = waveform_without.squeeze()
                
                filename_without = f"{output_dir}/test_{i+1:02d}_without_{timestamp}.wav"
                sf.write(filename_without, waveform_without, sampling_rate)
                
                duration_without = len(waveform_without) / sampling_rate
                print(f"   ✅ Sin puntuación: {filename_without} ({duration_without:.2f}s)")
                
            except Exception as e:
                print(f"   ❌ Error sin puntuación: {str(e)}")
                continue
            
            # Generar versión con puntuación
            try:
                result_with = tts(test['with'])
                waveform_with = result_with["audio"]
                sampling_rate = result_with["sampling_rate"]
                
                if isinstance(waveform_with, torch.Tensor):
                    waveform_with = waveform_with.cpu().numpy()
                
                if len(waveform_with.shape) > 1:
                    waveform_with = waveform_with.squeeze()
                
                filename_with = f"{output_dir}/test_{i+1:02d}_with_{timestamp}.wav"
                sf.write(filename_with, waveform_with, sampling_rate)
                
                duration_with = len(waveform_with) / sampling_rate
                print(f"   ✅ Con puntuación: {filename_with} ({duration_with:.2f}s)")
                
                # Comparar duraciones
                diff = duration_with - duration_without
                if abs(diff) > 0.1:  # Diferencia significativa
                    print(f"   📊 Diferencia de duración: {diff:+.2f}s {'(MÁS PAUSAS)' if diff > 0 else '(MENOS PAUSAS)'}")
                else:
                    print(f"   📊 Diferencia mínima: {diff:+.2f}s")
                
            except Exception as e:
                print(f"   ❌ Error con puntuación: {str(e)}")
                continue
        
        return True
        
    except Exception as e:
        print(f"❌ Error en test de puntuación: {str(e)}")
        return False

def analyze_dataset_examples():
    """Analiza ejemplos reales del dataset para ver patrones"""
    
    print(f"\n📚 ANALIZANDO EJEMPLOS DEL DATASET ORIGINAL")
    print("=" * 50)
    
    try:
        import pandas as pd
        df = pd.read_csv('data.csv', header=None, names=['audio', 'text'])
        
        # Buscar ejemplos con diferentes tipos de puntuación
        examples = {
            'Comas': df[df['text'].str.contains(',', regex=False)].head(3),
            'Interrogación': df[df['text'].str.contains('¿', regex=False)].head(3),
            'Exclamación': df[df['text'].str.contains('¡', regex=False)].head(3),
            'Dos puntos': df[df['text'].str.contains(':', regex=False)].head(3)
        }
        
        for punct_type, sample_df in examples.items():
            print(f"\n🔸 {punct_type}:")
            for i, row in sample_df.iterrows():
                print(f"   {row['text'][:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Error analizando dataset: {str(e)}")
        return False

if __name__ == "__main__":
    print("🔬 TEST DE APRENDIZAJE DE PUNTUACIÓN")
    print("=" * 60)
    
    # Analizar el dataset original
    analyze_dataset_examples()
    
    # Probar el modelo
    success = test_punctuation_learning()
    
    if success:
        print(f"\n🎉 Test completado!")
        print(f"📁 Revisa la carpeta 'punctuation_test'")
        print(f"🎧 Compara los archivos 'without' vs 'with'")
        print(f"👂 Escucha si hay diferencias en:")
        print(f"   • Pausas en comas")
        print(f"   • Entonación en interrogaciones")
        print(f"   • Énfasis en exclamaciones")
        print(f"   • Ritmo general")
    else:
        print(f"\n❌ El test falló")
    
    print(f"\n💡 CONCLUSIÓN:")
    print(f"Tu dataset SÍ tiene puntuación (61.6% puntos, 62.2% comas)")
    print(f"El modelo DEBERÍA haber aprendido las pausas y entonación")
    print(f"Si no es evidente, podríamos necesitar más entrenamiento")
