{"project_name": "mms_korean-finetuning", "push_to_hub": true, "hub_model_id": "ylacombe/mms-tts-kor-finetuned", "report_to": ["wandb"], "overwrite_output_dir": true, "output_dir": "./tmp/vits_finetuned_kor", "dataset_name": "PolyAI/minds14", "dataset_config_name": "ko-KR", "audio_column_name": "audio", "text_column_name": "transcription", "train_split_name": "train", "eval_split_name": "train", "full_generation_sample_text": "그는 괜찮은 척하려고 애쓰는 것 같았다.", "max_duration_in_seconds": 20, "min_duration_in_seconds": 1.0, "max_tokens_length": 500, "model_name_or_path": "ylacombe/mms-tts-kor-train", "preprocessing_num_workers": 4, "do_train": true, "num_train_epochs": 200, "max_train_samples": 150, "gradient_accumulation_steps": 1, "gradient_checkpointing": false, "per_device_train_batch_size": 16, "learning_rate": 2e-05, "adam_beta1": 0.8, "adam_beta2": 0.99, "warmup_ratio": 0.01, "group_by_length": false, "do_eval": true, "eval_steps": 50, "per_device_eval_batch_size": 16, "max_eval_samples": 25, "do_step_schedule_per_epoch": true, "weight_disc": 3, "weight_fmaps": 1, "weight_gen": 1, "weight_kl": 1.5, "weight_duration": 1, "weight_mel": 35, "fp16": true, "seed": 456, "uroman_path": "/home/<USER>/uroman"}