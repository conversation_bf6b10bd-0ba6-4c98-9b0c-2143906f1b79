"""
Custom dataset script for loading local Spanish audio dataset for Hugging Face datasets
"""

import os
import csv
import datasets
from datasets import Audio

_DESCRIPTION = """
Local Spanish TTS dataset with audio files and transcriptions.
"""

_HOMEPAGE = ""
_LICENSE = ""
_CITATION = ""

class SpanishDatasetConfig(datasets.BuilderConfig):
    """BuilderConfig for Spanish dataset."""
    
    def __init__(self, **kwargs):
        super(SpanishDatasetConfig, self).__init__(**kwargs)

class SpanishDataset(datasets.GeneratorBasedBuilder):
    """Spanish TTS dataset."""
    
    BUILDER_CONFIGS = [
        SpanishDatasetConfig(
            name="default",
            version=datasets.Version("1.0.0"),
            description="Default config for Spanish dataset",
        ),
    ]
    
    DEFAULT_CONFIG_NAME = "default"
    
    def _info(self):
        return datasets.DatasetInfo(
            description=_DESCRIPTION,
            features=datasets.Features({
                "audio": Audio(sampling_rate=22050),
                "text": datasets.Value("string"),
                "filename": datasets.Value("string"),
            }),
            homepage=_HOMEPAGE,
            license=_LICENSE,
            citation=_CITATION,
        )
    
    def _split_generators(self, dl_manager):
        """Returns SplitGenerators."""
        # The data directory should be the parent directory of this script
        data_dir = os.path.dirname(os.path.abspath(__file__))
        
        return [
            datasets.SplitGenerator(
                name=datasets.Split.TRAIN,
                gen_kwargs={
                    "data_dir": data_dir,
                    "split": "train",
                },
            ),
            datasets.SplitGenerator(
                name=datasets.Split.VALIDATION,
                gen_kwargs={
                    "data_dir": data_dir,
                    "split": "validation",
                },
            ),
        ]
    
    def _generate_examples(self, data_dir, split):
        """Yields examples."""
        csv_file = os.path.join(data_dir, "data.csv")
        audio_dir = os.path.join(data_dir, "audios")
        
        # Read all data first
        all_data = []
        with open(csv_file, 'r', encoding='utf-8') as f:
            csv_reader = csv.reader(f)
            for row in csv_reader:
                if len(row) >= 2:
                    filename = row[0]
                    text = row[1]
                    audio_path = os.path.join(audio_dir, filename)
                    
                    if os.path.exists(audio_path):
                        all_data.append({
                            "filename": filename,
                            "text": text,
                            "audio_path": audio_path
                        })
        
        # Split data (90% train, 10% validation)
        total_samples = len(all_data)
        train_size = int(0.9 * total_samples)
        
        if split == "train":
            data_subset = all_data[:train_size]
        else:  # validation
            data_subset = all_data[train_size:]
        
        # Generate examples
        for idx, example in enumerate(data_subset):
            yield idx, {
                "audio": example["audio_path"],
                "text": example["text"],
                "filename": example["filename"],
            }
