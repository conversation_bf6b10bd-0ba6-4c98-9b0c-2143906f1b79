{"project_name": "mms_spanish_finetuning_optimized", "push_to_hub": false, "hub_model_id": "my-spanish-tts-model-optimized", "report_to": [], "overwrite_output_dir": true, "output_dir": "./tmp/vits_finetuned_spanish_optimized", "dataset_name": "andrsvlz/spanish-tts-dataset", "dataset_config_name": null, "audio_column_name": "audio", "text_column_name": "text", "train_split_name": "train", "eval_split_name": "validation", "speaker_id_column_name": null, "override_speaker_embeddings": false, "filter_on_speaker_id": null, "full_generation_sample_text": "<PERSON><PERSON>, ¿cómo estás hoy? Espero que tengas un buen día. ¡Qué maravilloso es poder hablar con naturalidad!", "max_duration_in_seconds": 20, "min_duration_in_seconds": 1.0, "max_tokens_length": 500, "model_name_or_path": "ylacombe/mms-tts-spa-train", "preprocessing_num_workers": 6, "dataloader_num_workers": 6, "remove_unused_columns": false, "do_train": true, "num_train_epochs": 75, "gradient_accumulation_steps": 1, "gradient_checkpointing": true, "per_device_train_batch_size": 12, "learning_rate": 1.5e-05, "adam_beta1": 0.8, "adam_beta2": 0.99, "warmup_ratio": 0.02, "group_by_length": true, "do_eval": true, "eval_steps": 200, "per_device_eval_batch_size": 12, "max_eval_samples": 100, "do_step_schedule_per_epoch": true, "weight_disc": 3, "weight_fmaps": 1, "weight_gen": 1, "weight_kl": 1.5, "weight_duration": 1, "weight_mel": 35, "bf16": true, "fp16": false, "seed": 42, "save_steps": 400, "logging_steps": 25, "save_total_limit": 5, "load_best_model_at_end": true, "metric_for_best_model": "eval_loss", "greater_is_better": false, "evaluation_strategy": "steps", "save_strategy": "steps"}