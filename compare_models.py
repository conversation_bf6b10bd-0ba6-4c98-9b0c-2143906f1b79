#!/usr/bin/env python3
"""
Script para comparar el modelo entrenado con el modelo original
"""

import torch
from transformers import pipeline
import soundfile as sf
import numpy as np
from datetime import datetime
import os

def compare_models():
    """Compara el modelo entrenado con el original"""
    
    print("🔄 Comparando modelo entrenado vs modelo original...")
    
    # Rutas de los modelos
    trained_model = "./finetune-hf-vits/tmp/vits_finetuned_spanish"
    original_model = "ylacombe/mms-tts-spa-train"
    
    # Textos de comparación
    comparison_texts = [
        "Ho<PERSON>, ¿cómo estás hoy?",
        "¡Qué maravilloso día para probar este modelo!",
        "Buenos días, mi nombre es Andrea y soy tu asistente.",
        "La inteligencia artificial está revolucionando el mundo."
    ]
    
    output_dir = "model_comparison"
    os.makedirs(output_dir, exist_ok=True)
    
    device = 0 if torch.cuda.is_available() else -1
    
    try:
        print("🚀 Cargando modelo entrenado...")
        tts_trained = pipeline("text-to-speech", model=trained_model, device=device)
        
        print("🚀 Cargando modelo original...")
        tts_original = pipeline("text-to-speech", model=original_model, device=device)
        
        print(f"\n🎵 Generando comparaciones para {len(comparison_texts)} textos...")
        
        for i, text in enumerate(comparison_texts):
            print(f"\n📝 Texto {i+1}: '{text}'")
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Generar con modelo entrenado
            try:
                result_trained = tts_trained(text)
                waveform_trained = result_trained["audio"]
                sampling_rate = result_trained["sampling_rate"]
                
                if isinstance(waveform_trained, torch.Tensor):
                    waveform_trained = waveform_trained.cpu().numpy()
                
                if len(waveform_trained.shape) > 1:
                    waveform_trained = waveform_trained.squeeze()
                
                filename_trained = f"{output_dir}/trained_{i+1:02d}_{timestamp}.wav"
                sf.write(filename_trained, waveform_trained, sampling_rate)
                
                print(f"   ✅ Modelo entrenado: {filename_trained}")
                print(f"      📊 Duración: {len(waveform_trained)/sampling_rate:.2f}s")
                
            except Exception as e:
                print(f"   ❌ Error modelo entrenado: {str(e)}")
                continue
            
            # Generar con modelo original
            try:
                result_original = tts_original(text)
                waveform_original = result_original["audio"]
                sampling_rate = result_original["sampling_rate"]
                
                if isinstance(waveform_original, torch.Tensor):
                    waveform_original = waveform_original.cpu().numpy()
                
                if len(waveform_original.shape) > 1:
                    waveform_original = waveform_original.squeeze()
                
                filename_original = f"{output_dir}/original_{i+1:02d}_{timestamp}.wav"
                sf.write(filename_original, waveform_original, sampling_rate)
                
                print(f"   ✅ Modelo original: {filename_original}")
                print(f"      📊 Duración: {len(waveform_original)/sampling_rate:.2f}s")
                
            except Exception as e:
                print(f"   ❌ Error modelo original: {str(e)}")
                continue
        
        print(f"\n🎉 Comparación completada!")
        print(f"📁 Archivos guardados en: {output_dir}/")
        print(f"🎧 Compara los archivos 'trained_XX' vs 'original_XX'")
        
        # Resumen de archivos
        print(f"\n📋 Resumen de archivos generados:")
        files = os.listdir(output_dir)
        trained_files = [f for f in files if f.startswith('trained_')]
        original_files = [f for f in files if f.startswith('original_')]
        
        print(f"   🎯 Modelo entrenado: {len(trained_files)} archivos")
        print(f"   📚 Modelo original: {len(original_files)} archivos")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en comparación: {str(e)}")
        return False

def generate_summary():
    """Genera un resumen de todas las pruebas realizadas"""
    
    print("\n📊 RESUMEN DE PRUEBAS DEL MODELO TTS ESPAÑOL")
    print("=" * 60)
    
    # Contar archivos en cada directorio
    directories = ["generated_audio", "prosody_tests", "long_sentences", "model_comparison"]
    
    total_files = 0
    for directory in directories:
        if os.path.exists(directory):
            files = os.listdir(directory)
            wav_files = [f for f in files if f.endswith('.wav')]
            total_files += len(wav_files)
            print(f"📁 {directory}: {len(wav_files)} archivos de audio")
    
    print(f"\n🎵 Total de archivos de audio generados: {total_files}")
    
    print(f"\n✅ CARACTERÍSTICAS PROBADAS:")
    print(f"   🎭 Puntuación y prosodia (8 pruebas)")
    print(f"   📚 Oraciones largas (4 pruebas)")
    print(f"   🔄 Comparación con modelo original")
    print(f"   🎯 Textos básicos de prueba")
    
    print(f"\n🎧 PARA EVALUAR EL MODELO:")
    print(f"   • Escucha los archivos en cada carpeta")
    print(f"   • Compara 'trained_XX' vs 'original_XX'")
    print(f"   • Evalúa pausas, entonación y naturalidad")
    print(f"   • Verifica la pronunciación española")
    
    print(f"\n🏆 ¡ENTRENAMIENTO COMPLETADO EXITOSAMENTE!")

if __name__ == "__main__":
    print("🔍 Comparación de Modelos TTS")
    print("=" * 40)
    
    success = compare_models()
    
    if success:
        generate_summary()
    else:
        print("\n⚠️  La comparación falló")
        print("🔧 Revisa la configuración y conexión")
    
    print("\n✨ Proceso completado!")
