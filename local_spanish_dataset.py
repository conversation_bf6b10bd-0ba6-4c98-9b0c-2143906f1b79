#!/usr/bin/env python3
"""
Custom dataset script for loading local Spanish audio dataset
"""

import os
import csv
from datasets import Dataset, DatasetDict, Audio

def load_local_spanish_dataset(data_dir=".", audio_dir="audios", csv_file="data.csv"):
    """
    Load the local Spanish dataset from CSV and audio files

    Args:
        data_dir: Directory containing the CSV file
        audio_dir: Directory containing audio files (relative to data_dir)
        csv_file: Name of the CSV file

    Returns:
        DatasetDict with train and validation splits
    """

    # Read the CSV file
    csv_path = os.path.join(data_dir, csv_file)
    audio_base_path = os.path.join(data_dir, audio_dir)

    filenames = []
    texts = []
    audio_paths = []

    with open(csv_path, 'r', encoding='utf-8') as f:
        csv_reader = csv.reader(f)
        for row in csv_reader:
            if len(row) >= 2:
                filename = row[0]
                text = row[1]
                audio_path = os.path.join(audio_base_path, filename)

                # Check if audio file exists
                if os.path.exists(audio_path):
                    filenames.append(filename)
                    texts.append(text)
                    audio_paths.append(audio_path)

    print(f"Dataset loaded with {len(filenames)} samples")

    # Create dataset
    dataset_dict = {
        'audio': audio_paths,
        'text': texts,
        'filename': filenames
    }

    # Create Hugging Face dataset
    dataset = Dataset.from_dict(dataset_dict)

    # Cast audio column to Audio feature
    dataset = dataset.cast_column("audio", Audio(sampling_rate=22050))

    # Split into train and validation (90/10 split)
    train_test_split = dataset.train_test_split(test_size=0.1, seed=42)

    return DatasetDict({
        'train': train_test_split['train'],
        'validation': train_test_split['test']
    })

def get_dataset_info(dataset_dict):
    """Print information about the dataset"""
    print("\n=== Dataset Information ===")
    for split_name, split_data in dataset_dict.items():
        print(f"{split_name}: {len(split_data)} samples")
        
        # Sample a few examples
        print(f"\nSample from {split_name}:")
        for i in range(min(3, len(split_data))):
            sample = split_data[i]
            print(f"  File: {sample['filename']}")
            print(f"  Text: {sample['text'][:100]}...")
            if 'audio' in sample:
                print(f"  Audio shape: {sample['audio']['array'].shape}")
                print(f"  Sample rate: {sample['audio']['sampling_rate']}")
            print()

if __name__ == "__main__":
    # Test the dataset loading
    dataset = load_local_spanish_dataset()
    get_dataset_info(dataset)
