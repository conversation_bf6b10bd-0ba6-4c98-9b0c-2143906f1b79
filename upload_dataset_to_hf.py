#!/usr/bin/env python3
"""
Upload the Spanish TTS dataset to Hugging Face Hub (private)
"""

import os
import csv
from datasets import Dataset, DatasetDict, Audio
from huggingface_hub import <PERSON>f<PERSON><PERSON>

def create_and_upload_dataset(repo_name="spanish-tts-dataset"):
    """Create and upload the dataset to HF Hub"""
    
    print("Preparing dataset...")
    
    # Read the original CSV
    data_dir = "."
    csv_file = "data.csv"
    audio_dir = "audios"
    
    csv_path = os.path.join(data_dir, csv_file)
    audio_base_path = os.path.join(data_dir, audio_dir)
    
    # Collect all valid data
    all_data = []
    print("Reading CSV and checking audio files...")
    
    with open(csv_path, 'r', encoding='utf-8') as f:
        csv_reader = csv.reader(f)
        for i, row in enumerate(csv_reader):
            if len(row) >= 2:
                filename = row[0]
                text = row[1]
                audio_path = os.path.join(audio_base_path, filename)
                
                if os.path.exists(audio_path):
                    all_data.append({
                        "audio": audio_path,
                        "text": text,
                        "filename": filename
                    })
            
            # Progress indicator
            if (i + 1) % 1000 == 0:
                print(f"Processed {i + 1} rows...")
    
    print(f"Found {len(all_data)} valid samples")
    
    # Create dataset
    print("Creating Hugging Face dataset...")
    dataset = Dataset.from_list(all_data)
    
    # Cast audio column
    print("Processing audio files...")
    dataset = dataset.cast_column("audio", Audio(sampling_rate=22050))
    
    # Split into train/validation (90/10)
    print("Splitting dataset...")
    train_test_split = dataset.train_test_split(test_size=0.1, seed=42)
    
    dataset_dict = DatasetDict({
        'train': train_test_split['train'],
        'validation': train_test_split['test']
    })
    
    print(f"Dataset created:")
    print(f"  Train samples: {len(dataset_dict['train'])}")
    print(f"  Validation samples: {len(dataset_dict['validation'])}")
    
    # Upload to HF Hub (private)
    print(f"Uploading dataset to Hugging Face Hub as private repo: {repo_name}")
    
    try:
        dataset_dict.push_to_hub(
            repo_id=repo_name,
            private=True,  # Make it private
            commit_message="Upload Spanish TTS dataset"
        )
        print(f"✅ Dataset uploaded successfully to: https://huggingface.co/datasets/{repo_name}")
        print("⚠️  Dataset is PRIVATE - only you can access it")
        
        return repo_name
        
    except Exception as e:
        print(f"❌ Error uploading dataset: {e}")
        print("Make sure you're logged in with: huggingface-cli login")
        return None

def update_training_config(dataset_repo_name):
    """Update the training config to use the HF dataset"""
    
    config_file = "finetune_spanish_config.json"
    
    # Read current config
    import json
    with open(config_file, 'r') as f:
        config = json.load(f)
    
    # Update dataset settings
    config["dataset_name"] = dataset_repo_name
    config["dataset_config_name"] = None
    config["train_split_name"] = "train"
    config["eval_split_name"] = "validation"
    
    # Save updated config
    with open(config_file, 'w') as f:
        json.dump(config, f, indent=4)
    
    print(f"✅ Updated training config: {config_file}")

if __name__ == "__main__":
    print("🚀 Starting dataset upload to Hugging Face Hub...")
    print("📝 This will create a PRIVATE dataset repository")
    
    # Get username for repo name
    try:
        from huggingface_hub import whoami
        user_info = whoami()
        username = user_info['name']
        repo_name = f"{username}/spanish-tts-dataset"
        print(f"👤 Using username: {username}")
    except:
        print("⚠️  Could not get username, using default repo name")
        repo_name = "spanish-tts-dataset"
    
    # Create and upload dataset
    uploaded_repo = create_and_upload_dataset(repo_name)
    
    if uploaded_repo:
        # Update training config
        update_training_config(uploaded_repo)
        
        print("\n🎉 All done! Next steps:")
        print("1. Your dataset is now private on Hugging Face Hub")
        print("2. The training config has been updated")
        print("3. You can now run the training with:")
        print("   cd finetune-hf-vits")
        print("   accelerate launch run_vits_finetuning.py ../finetune_spanish_config.json")
    else:
        print("\n❌ Upload failed. Please check your Hugging Face login and try again.")
