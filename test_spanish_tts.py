#!/usr/bin/env python3
"""
Script para probar el modelo TTS español entrenado
"""

import torch
from transformers import VitsModel, VitsTokenizer, AutoFeatureExtractor
import soundfile as sf
import numpy as np
from datetime import datetime
import os

def test_spanish_tts():
    """Prueba el modelo TTS español entrenado"""
    
    # Ruta del modelo entrenado
    model_path = "./finetune-hf-vits/tmp/vits_finetuned_spanish"
    
    print("🚀 Cargando modelo TTS español entrenado...")
    print(f"📁 Ruta del modelo: {model_path}")
    
    try:
        # Cargar el modelo y componentes
        model = VitsModel.from_pretrained(model_path)
        tokenizer = VitsTokenizer.from_pretrained(model_path)
        feature_extractor = AutoFeatureExtractor.from_pretrained(model_path)
        
        # Mover a GPU si está disponible
        device = "cuda" if torch.cuda.is_available() else "cpu"
        model = model.to(device)
        print(f"🔥 Modelo cargado en: {device}")
        
        # Textos de prueba con diferentes características
        test_texts = [
            "Ho<PERSON>, ¿cómo estás hoy?",
            "¡Qué maravilloso día para entrenar un modelo de voz!",
            "Buenos días. Mi nombre es Andrea y soy tu asistente virtual.",
            "¿Podrías ayudarme con esta tarea, por favor?",
            "Uno, dos, tres, cuatro, cinco. Los números son importantes.",
            "La inteligencia artificial está revolucionando el mundo.",
            "¡Excelente! El entrenamiento ha sido un éxito completo.",
            "Gracias por tu paciencia durante este proceso de aprendizaje."
        ]
        
        # Crear directorio para los audios generados
        output_dir = "generated_audio"
        os.makedirs(output_dir, exist_ok=True)
        
        print(f"\n🎵 Generando {len(test_texts)} audios de prueba...")
        
        for i, text in enumerate(test_texts):
            print(f"\n📝 Texto {i+1}: '{text}'")
            
            try:
                # Tokenizar el texto
                inputs = tokenizer(text, return_tensors="pt").to(device)
                
                # Generar audio
                with torch.no_grad():
                    outputs = model(**inputs)
                    waveform = outputs.waveform.squeeze().cpu().numpy()
                
                # Guardar el audio
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"{output_dir}/test_{i+1:02d}_{timestamp}.wav"
                
                # Normalizar el audio
                if len(waveform.shape) > 1:
                    waveform = waveform[0]  # Tomar el primer canal si es estéreo
                
                # Asegurar que esté en el rango correcto
                waveform = np.clip(waveform, -1.0, 1.0)
                
                # Guardar con la frecuencia de muestreo del modelo
                sampling_rate = feature_extractor.sampling_rate
                sf.write(filename, waveform, sampling_rate)
                
                print(f"✅ Audio guardado: {filename}")
                print(f"   📊 Duración: {len(waveform)/sampling_rate:.2f} segundos")
                print(f"   🔊 Frecuencia: {sampling_rate} Hz")
                
            except Exception as e:
                print(f"❌ Error generando audio para '{text}': {str(e)}")
                continue
        
        print(f"\n🎉 ¡Prueba completada!")
        print(f"📁 Los audios se guardaron en: {output_dir}/")
        print(f"🎧 Puedes reproducir los archivos .wav para escuchar los resultados")
        
        # Información adicional del modelo
        print(f"\n📋 Información del modelo:")
        print(f"   🏗️  Arquitectura: VITS (Variational Inference TTS)")
        print(f"   🗣️  Idioma: Español")
        print(f"   📚 Dataset: {len(test_texts)} muestras de prueba")
        print(f"   🎯 Modelo base: ylacombe/mms-tts-spa-train")
        
        return True
        
    except Exception as e:
        print(f"❌ Error cargando el modelo: {str(e)}")
        print(f"🔍 Verifica que el modelo esté en: {model_path}")
        return False

if __name__ == "__main__":
    print("🎤 Probador de Modelo TTS Español")
    print("=" * 50)
    
    success = test_spanish_tts()
    
    if success:
        print("\n✨ ¡El modelo funciona correctamente!")
        print("🎵 Revisa los archivos de audio generados")
    else:
        print("\n💥 Hubo problemas con el modelo")
        print("🔧 Revisa la configuración y vuelve a intentar")
