#!/usr/bin/env python3
"""
Prepare the dataset for training by creating proper CSV files with full paths
"""

import os
import csv
import shutil
from datasets import Dataset, DatasetDict, Audio

def create_training_dataset():
    """Create a dataset that can be loaded by the training script"""
    
    # Read the original CSV
    data_dir = "."
    csv_file = "data.csv"
    audio_dir = "audios"
    
    csv_path = os.path.join(data_dir, csv_file)
    audio_base_path = os.path.join(data_dir, audio_dir)
    
    # Collect all valid data
    all_data = []
    with open(csv_path, 'r', encoding='utf-8') as f:
        csv_reader = csv.reader(f)
        for row in csv_reader:
            if len(row) >= 2:
                filename = row[0]
                text = row[1]
                audio_path = os.path.join(audio_base_path, filename)
                
                if os.path.exists(audio_path):
                    all_data.append({
                        "audio": audio_path,
                        "text": text,
                        "filename": filename
                    })
    
    print(f"Found {len(all_data)} valid samples")
    
    # Create dataset
    dataset = Dataset.from_list(all_data)
    
    # Cast audio column
    dataset = dataset.cast_column("audio", Audio(sampling_rate=22050))
    
    # Split into train/validation
    train_test_split = dataset.train_test_split(test_size=0.1, seed=42)
    
    dataset_dict = DatasetDict({
        'train': train_test_split['train'],
        'validation': train_test_split['test']
    })
    
    # Save the dataset
    dataset_dict.save_to_disk("./spanish_tts_dataset")
    
    print(f"Dataset saved to ./spanish_tts_dataset")
    print(f"Train samples: {len(dataset_dict['train'])}")
    print(f"Validation samples: {len(dataset_dict['validation'])}")
    
    return dataset_dict

if __name__ == "__main__":
    dataset = create_training_dataset()
    
    # Test loading
    print("\nTesting dataset loading...")
    sample = dataset['train'][0]
    print(f"Sample text: {sample['text'][:100]}...")
    print(f"Audio shape: {sample['audio']['array'].shape}")
    print(f"Sample rate: {sample['audio']['sampling_rate']}")
