#!/usr/bin/env python3
"""
Make all your Hugging Face repositories private
"""

from huggingface_hub import Hf<PERSON><PERSON>, whoami
import time

def make_all_repos_private():
    """Make all user repositories private"""
    
    try:
        # Get user info
        user_info = whoami()
        username = user_info['name']
        print(f"👤 User: {username}")
        
        # Initialize API
        api = HfApi()
        
        # Get all user repositories (models and datasets)
        print("🔍 Fetching your repositories...")
        
        # Get model repositories
        model_repos = list(api.list_models(author=username))
        dataset_repos = list(api.list_datasets(author=username))
        
        print(f"📊 Found {len(model_repos)} model repos and {len(dataset_repos)} dataset repos")
        
        if len(model_repos) == 0 and len(dataset_repos) == 0:
            print("✅ No repositories found to make private")
            return
        
        # Ask for confirmation
        print("\n⚠️  This will make ALL your repositories PRIVATE")
        print("📝 List of repositories that will be made private:")
        
        all_repos = []
        
        for repo in model_repos:
            repo_id = repo.id
            is_private = getattr(repo, 'private', False)
            status = "🔒 Already private" if is_private else "🔓 Will be made private"
            print(f"  📦 Model: {repo_id} - {status}")
            if not is_private:
                all_repos.append(('model', repo_id))
        
        for repo in dataset_repos:
            repo_id = repo.id
            is_private = getattr(repo, 'private', False)
            status = "🔒 Already private" if is_private else "🔓 Will be made private"
            print(f"  📊 Dataset: {repo_id} - {status}")
            if not is_private:
                all_repos.append(('dataset', repo_id))
        
        if not all_repos:
            print("✅ All repositories are already private!")
            return
        
        print(f"\n🔄 Will make {len(all_repos)} repositories private")
        
        # Uncomment the next lines if you want to require confirmation
        # response = input("Continue? (y/N): ")
        # if response.lower() != 'y':
        #     print("❌ Cancelled")
        #     return
        
        # Make repositories private
        print("\n🔒 Making repositories private...")
        
        success_count = 0
        error_count = 0
        
        for repo_type, repo_id in all_repos:
            try:
                if repo_type == 'model':
                    api.update_repo_visibility(repo_id=repo_id, private=True)
                else:  # dataset
                    api.update_repo_visibility(repo_id=repo_id, private=True, repo_type="dataset")
                
                print(f"  ✅ {repo_type.capitalize()}: {repo_id}")
                success_count += 1
                
                # Small delay to avoid rate limiting
                time.sleep(0.5)
                
            except Exception as e:
                print(f"  ❌ {repo_type.capitalize()}: {repo_id} - Error: {e}")
                error_count += 1
        
        print(f"\n🎉 Done!")
        print(f"✅ Successfully made {success_count} repositories private")
        if error_count > 0:
            print(f"❌ {error_count} repositories had errors")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print("Make sure you're logged in with: huggingface-cli login")

if __name__ == "__main__":
    print("🔒 Making all your Hugging Face repositories private...")
    print("⚠️  Make sure you're logged in first: huggingface-cli login")
    print()
    
    make_all_repos_private()
