#!/usr/bin/env python3
"""
Training script for Spanish TTS model using local dataset
"""

import os
import sys
import json
import subprocess
from local_spanish_dataset import load_local_spanish_dataset

def main():
    # Load the dataset first to verify it works
    print("Loading local Spanish dataset...")
    dataset = load_local_spanish_dataset()
    print(f"Dataset loaded successfully!")
    print(f"Train samples: {len(dataset['train'])}")
    print(f"Validation samples: {len(dataset['validation'])}")
    
    # Create a temporary dataset script that the training can use
    dataset_script_content = '''
import os
import csv
from datasets import Dataset, DatasetDict, Audio

def load_dataset(data_dir=".", **kwargs):
    """Load the local Spanish dataset"""
    csv_file = "data.csv"
    audio_dir = "audios"
    
    csv_path = os.path.join(data_dir, csv_file)
    audio_base_path = os.path.join(data_dir, audio_dir)
    
    filenames = []
    texts = []
    audio_paths = []
    
    with open(csv_path, 'r', encoding='utf-8') as f:
        csv_reader = csv.reader(f)
        for row in csv_reader:
            if len(row) >= 2:
                filename = row[0]
                text = row[1]
                audio_path = os.path.join(audio_base_path, filename)
                
                if os.path.exists(audio_path):
                    filenames.append(filename)
                    texts.append(text)
                    audio_paths.append(audio_path)
    
    dataset_dict = {
        'audio': audio_paths,
        'text': texts,
        'filename': filenames
    }
    
    dataset = Dataset.from_dict(dataset_dict)
    dataset = dataset.cast_column("audio", Audio(sampling_rate=22050))
    
    train_test_split = dataset.train_test_split(test_size=0.1, seed=42)
    
    return DatasetDict({
        'train': train_test_split['train'],
        'validation': train_test_split['test']
    })
'''
    
    # Write the dataset script
    with open('local_dataset_loader.py', 'w') as f:
        f.write(dataset_script_content)
    
    # Update the config to use our dataset loader
    config_path = 'finetune_spanish_config.json'
    with open(config_path, 'r') as f:
        config = json.load(f)
    
    # Update dataset path to use our custom loader
    config['dataset_name'] = './local_dataset_loader.py'
    
    # Save updated config
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=4)
    
    print(f"Updated configuration saved to {config_path}")
    
    # Change to the finetune-hf-vits directory
    os.chdir('finetune-hf-vits')
    
    # Copy our files to the training directory
    subprocess.run(['cp', '../finetune_spanish_config.json', '.'], check=True)
    subprocess.run(['cp', '../local_dataset_loader.py', '.'], check=True)
    
    # Run the training
    print("Starting training...")
    cmd = [
        'accelerate', 'launch', 
        'run_vits_finetuning.py', 
        'finetune_spanish_config.json'
    ]
    
    print(f"Running command: {' '.join(cmd)}")
    result = subprocess.run(cmd, capture_output=False)
    
    if result.returncode == 0:
        print("Training completed successfully!")
    else:
        print(f"Training failed with return code: {result.returncode}")
        return result.returncode
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
