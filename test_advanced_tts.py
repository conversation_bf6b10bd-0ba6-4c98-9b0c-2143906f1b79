#!/usr/bin/env python3
"""
Script avanzado para probar el modelo TTS español con diferentes características
"""

import torch
from transformers import pipeline
import soundfile as sf
import numpy as np
from datetime import datetime
import os

def test_punctuation_and_prosody():
    """Prueba específicamente puntuación, pausas y prosodia"""
    
    model_path = "./finetune-hf-vits/tmp/vits_finetuned_spanish"
    
    print("🎭 Probando puntuación, pausas y prosodia...")
    
    try:
        device = 0 if torch.cuda.is_available() else -1
        tts = pipeline("text-to-speech", model=model_path, device=device)
        
        # Textos específicos para probar características
        prosody_tests = [
            {
                "text": "Hola. ¿Cómo estás? ¡Muy bien, gracias!",
                "description": "Puntuación básica (punto, interrogación, exclamación)"
            },
            {
                "text": "Uno, dos, tres... cuatro, cinco, seis.",
                "description": "Comas y puntos suspensivos"
            },
            {
                "text": "¿Realmente funciona esto? ¡Sí, funciona perfectamente!",
                "description": "Entonación interrogativa y exclamativa"
            },
            {
                "text": "Buenos días, señora García; espero que tenga un excelente día.",
                "description": "Punto y coma, pausas naturales"
            },
            {
                "text": "La casa es grande, bonita y cómoda.",
                "description": "Enumeración con comas"
            },
            {
                "text": "¡Atención! Esto es muy importante: escucha con cuidado.",
                "description": "Exclamación y dos puntos"
            },
            {
                "text": "Andrea dijo: \"Hola, ¿cómo están todos hoy?\"",
                "description": "Discurso directo con comillas"
            },
            {
                "text": "El modelo aprende pausas... ritmo... y entonación natural.",
                "description": "Múltiples pausas y ritmo"
            }
        ]
        
        output_dir = "prosody_tests"
        os.makedirs(output_dir, exist_ok=True)
        
        print(f"\n🎵 Generando {len(prosody_tests)} pruebas de prosodia...")
        
        for i, test in enumerate(prosody_tests):
            text = test["text"]
            description = test["description"]
            
            print(f"\n📝 Prueba {i+1}: {description}")
            print(f"   Texto: '{text}'")
            
            try:
                result = tts(text)
                waveform = result["audio"]
                sampling_rate = result["sampling_rate"]
                
                if isinstance(waveform, torch.Tensor):
                    waveform = waveform.cpu().numpy()
                
                if len(waveform.shape) > 1:
                    waveform = waveform.squeeze()
                
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"{output_dir}/prosody_{i+1:02d}_{timestamp}.wav"
                
                sf.write(filename, waveform, sampling_rate)
                
                print(f"   ✅ Audio guardado: {filename}")
                print(f"   📊 Duración: {len(waveform)/sampling_rate:.2f} segundos")
                
            except Exception as e:
                print(f"   ❌ Error: {str(e)}")
                continue
        
        return True
        
    except Exception as e:
        print(f"❌ Error en pruebas de prosodia: {str(e)}")
        return False

def test_long_sentences():
    """Prueba oraciones más largas y complejas"""
    
    model_path = "./finetune-hf-vits/tmp/vits_finetuned_spanish"
    
    print("\n📚 Probando oraciones largas y complejas...")
    
    try:
        device = 0 if torch.cuda.is_available() else -1
        tts = pipeline("text-to-speech", model=model_path, device=device)
        
        long_texts = [
            "La inteligencia artificial ha revolucionado completamente la forma en que interactuamos con la tecnología, permitiendo crear sistemas que pueden entender y generar lenguaje natural de manera sorprendentemente efectiva.",
            
            "En un lugar de la Mancha, de cuyo nombre no quiero acordarme, no ha mucho tiempo que vivía un hidalgo de los de lanza en astillero, adarga antigua, rocín flaco y galgo corredor.",
            
            "El entrenamiento de modelos de síntesis de voz requiere grandes cantidades de datos de audio de alta calidad, junto con sus transcripciones correspondientes, para poder aprender los patrones complejos del habla humana.",
            
            "¿Sabías que los modelos de texto a voz modernos pueden generar audio tan realista que es prácticamente indistinguible de una grabación humana real? ¡Es verdaderamente impresionante!"
        ]
        
        output_dir = "long_sentences"
        os.makedirs(output_dir, exist_ok=True)
        
        for i, text in enumerate(long_texts):
            print(f"\n📝 Oración larga {i+1}:")
            print(f"   '{text[:50]}...'")
            
            try:
                result = tts(text)
                waveform = result["audio"]
                sampling_rate = result["sampling_rate"]
                
                if isinstance(waveform, torch.Tensor):
                    waveform = waveform.cpu().numpy()
                
                if len(waveform.shape) > 1:
                    waveform = waveform.squeeze()
                
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"{output_dir}/long_{i+1:02d}_{timestamp}.wav"
                
                sf.write(filename, waveform, sampling_rate)
                
                print(f"   ✅ Audio guardado: {filename}")
                print(f"   📊 Duración: {len(waveform)/sampling_rate:.2f} segundos")
                print(f"   📏 Caracteres: {len(text)}")
                
            except Exception as e:
                print(f"   ❌ Error: {str(e)}")
                continue
        
        return True
        
    except Exception as e:
        print(f"❌ Error en oraciones largas: {str(e)}")
        return False

if __name__ == "__main__":
    print("🎤 Pruebas Avanzadas del Modelo TTS Español")
    print("=" * 60)
    
    # Probar características específicas
    success1 = test_punctuation_and_prosody()
    success2 = test_long_sentences()
    
    if success1 and success2:
        print("\n🎉 ¡Todas las pruebas completadas exitosamente!")
        print("📁 Revisa las carpetas 'prosody_tests' y 'long_sentences'")
        print("🎧 Escucha los audios para evaluar:")
        print("   • Pausas naturales en puntuación")
        print("   • Entonación interrogativa y exclamativa") 
        print("   • Ritmo y fluidez en oraciones largas")
        print("   • Calidad general de la síntesis")
    else:
        print("\n⚠️  Algunas pruebas fallaron")
        print("🔧 Revisa los errores y la configuración del modelo")
    
    print("\n✨ Análisis completado!")
