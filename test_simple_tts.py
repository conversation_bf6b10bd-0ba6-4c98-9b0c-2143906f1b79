#!/usr/bin/env python3
"""
Script simple para probar el modelo TTS español entrenado
"""

import torch
from transformers import pipeline
import soundfile as sf
import numpy as np
from datetime import datetime
import os

def test_spanish_tts_simple():
    """Prueba el modelo TTS español usando pipeline"""
    
    # Ruta del modelo entrenado
    model_path = "./finetune-hf-vits/tmp/vits_finetuned_spanish"
    
    print("🚀 Cargando modelo TTS español con pipeline...")
    print(f"📁 Ruta del modelo: {model_path}")
    
    try:
        # Crear pipeline de TTS
        device = 0 if torch.cuda.is_available() else -1
        tts = pipeline("text-to-speech", 
                      model=model_path, 
                      device=device)
        
        print(f"🔥 Pipeline cargado en: {'GPU' if device == 0 else 'CPU'}")
        
        # Textos de prueba
        test_texts = [
            "Hola, ¿cómo estás hoy?",
            "¡Qué maravilloso día!",
            "Buenos días, mi nombre es Andrea.",
            "Gracias por entrenar este modelo.",
            "La inteligencia artificial es increíble."
        ]
        
        # Crear directorio para los audios
        output_dir = "generated_audio"
        os.makedirs(output_dir, exist_ok=True)
        
        print(f"\n🎵 Generando {len(test_texts)} audios...")
        
        for i, text in enumerate(test_texts):
            print(f"\n📝 Texto {i+1}: '{text}'")
            
            try:
                # Generar audio
                result = tts(text)
                
                # Extraer waveform y sampling rate
                waveform = result["audio"]
                sampling_rate = result["sampling_rate"]
                
                # Guardar el audio
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"{output_dir}/test_{i+1:02d}_{timestamp}.wav"
                
                # Normalizar si es necesario
                if isinstance(waveform, torch.Tensor):
                    waveform = waveform.cpu().numpy()
                
                if len(waveform.shape) > 1:
                    waveform = waveform.squeeze()
                
                # Guardar audio
                sf.write(filename, waveform, sampling_rate)
                
                print(f"✅ Audio guardado: {filename}")
                print(f"   📊 Duración: {len(waveform)/sampling_rate:.2f} segundos")
                print(f"   🔊 Frecuencia: {sampling_rate} Hz")
                
            except Exception as e:
                print(f"❌ Error generando audio: {str(e)}")
                continue
        
        print(f"\n🎉 ¡Prueba completada!")
        print(f"📁 Audios guardados en: {output_dir}/")
        
        return True
        
    except Exception as e:
        print(f"❌ Error con pipeline: {str(e)}")
        return False

def test_with_original_model():
    """Prueba con el modelo original como fallback"""
    
    print("\n🔄 Probando con modelo original como referencia...")
    
    try:
        # Usar el modelo original
        tts = pipeline("text-to-speech", 
                      model="ylacombe/mms-tts-spa-train",
                      device=0 if torch.cuda.is_available() else -1)
        
        text = "Hola, este es el modelo original de referencia."
        result = tts(text)
        
        # Guardar audio de referencia
        output_dir = "generated_audio"
        os.makedirs(output_dir, exist_ok=True)
        
        waveform = result["audio"]
        sampling_rate = result["sampling_rate"]
        
        if isinstance(waveform, torch.Tensor):
            waveform = waveform.cpu().numpy()
        
        if len(waveform.shape) > 1:
            waveform = waveform.squeeze()
        
        filename = f"{output_dir}/original_reference.wav"
        sf.write(filename, waveform, sampling_rate)
        
        print(f"✅ Audio de referencia guardado: {filename}")
        return True
        
    except Exception as e:
        print(f"❌ Error con modelo original: {str(e)}")
        return False

if __name__ == "__main__":
    print("🎤 Probador Simple de Modelo TTS Español")
    print("=" * 50)
    
    # Intentar con el modelo entrenado
    success = test_spanish_tts_simple()
    
    if not success:
        print("\n🔄 Intentando con modelo original...")
        test_with_original_model()
    
    print("\n✨ Proceso completado!")
    print("🎧 Reproduce los archivos .wav para escuchar los resultados")
